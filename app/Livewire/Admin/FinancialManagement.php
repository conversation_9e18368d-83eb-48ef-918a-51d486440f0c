<?php

namespace App\Livewire\Admin;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Branch;
use App\Models\BranchTransaction;
use App\Models\Loan;
use App\Models\Installment;
use App\Models\Member;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class FinancialManagement extends Component
{
    use WithPagination;

    public $selectedBranch = 'all';
    public $dateFrom;
    public $dateTo;
    public $transactionType = 'all';
    public $search = '';
    
    // Financial Overview Data
    public $financialOverview = [];
    public $branchFinancials = [];
    public $monthlyTrends = [];
    
    // Transaction Modal
    public $showTransactionModal = false;
    public $transactionAmount = '';
    public $transactionDescription = '';
    public $transactionBranch = '';
    public $transactionTypeModal = 'income';
    
    protected $rules = [
        'transactionAmount' => 'required|numeric|min:0',
        'transactionDescription' => 'required|string|max:500',
        'transactionBranch' => 'required|exists:branches,id',
        'transactionTypeModal' => 'required|in:income,expense'
    ];
    
    protected $messages = [
        'transactionAmount.required' => 'Transaction amount is required.',
        'transactionAmount.numeric' => 'Amount must be a valid number.',
        'transactionDescription.required' => 'Description is required.',
        'transactionBranch.required' => 'Please select a branch.',
        'transactionBranch.exists' => 'Selected branch does not exist.'
    ];

    public function mount()
    {
        $this->dateFrom = now()->startOfMonth()->format('Y-m-d');
        $this->dateTo = now()->format('Y-m-d');
        $this->loadFinancialData();
    }

    public function updatedSelectedBranch()
    {
        $this->resetPage();
        $this->loadFinancialData();
    }

    public function updatedDateFrom()
    {
        $this->resetPage();
        $this->loadFinancialData();
    }

    public function updatedDateTo()
    {
        $this->resetPage();
        $this->loadFinancialData();
    }

    public function updatedTransactionType()
    {
        $this->resetPage();
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function loadFinancialData()
    {
        $this->loadFinancialOverview();
        $this->loadBranchFinancials();
        $this->loadMonthlyTrends();
    }

    public function loadFinancialOverview()
    {
        $dateFrom = Carbon::parse($this->dateFrom)->startOfDay();
        $dateTo = Carbon::parse($this->dateTo)->endOfDay();
        
        // Total loan disbursements
        $totalLoans = Loan::whereBetween('disbursement_date', [$dateFrom, $dateTo]);
        if ($this->selectedBranch !== 'all') {
            $totalLoans->whereHas('loanApplication.member', function($query) {
                $query->where('branch_id', $this->selectedBranch);
            });
        }
        $totalLoanAmount = $totalLoans->sum('loan_amount');
        
        // Total installment collections
        $totalCollections = Installment::where('status', 'paid')
            ->whereBetween('paid_date', [$dateFrom, $dateTo]);
        if ($this->selectedBranch !== 'all') {
            $totalCollections->whereHas('loan.loanApplication.member', function($query) {
                $query->where('branch_id', $this->selectedBranch);
            });
        }
        $totalCollectionAmount = $totalCollections->sum('installment_amount');
        
        // Branch transactions (income/expense)
        $branchTransactionsQuery = BranchTransaction::whereBetween('transaction_date', [$dateFrom, $dateTo]);
        if ($this->selectedBranch !== 'all') {
            $branchTransactionsQuery->where('branch_id', $this->selectedBranch);
        }
        
        $totalIncome = (clone $branchTransactionsQuery)->where('transaction_type', 'income')->sum('amount');
        $totalExpense = (clone $branchTransactionsQuery)->where('transaction_type', 'expense')->sum('amount');
        
        // Outstanding amounts
        $outstandingQuery = Installment::where('status', 'pending');
        if ($this->selectedBranch !== 'all') {
            $outstandingQuery->whereHas('loan.loanApplication.member', function($query) {
                $query->where('branch_id', $this->selectedBranch);
            });
        }
        $totalOutstanding = $outstandingQuery->sum('installment_amount');
        
        // Overdue amounts
        $overdueQuery = Installment::where('status', 'pending')
            ->where('due_date', '<', now());
        if ($this->selectedBranch !== 'all') {
            $overdueQuery->whereHas('loan.loanApplication.member', function($query) {
                $query->where('branch_id', $this->selectedBranch);
            });
        }
        $totalOverdue = $overdueQuery->sum('installment_amount');
        
        $this->financialOverview = [
            'total_loans' => $totalLoanAmount,
            'total_collections' => $totalCollectionAmount,
            'total_income' => $totalIncome,
            'total_expense' => $totalExpense,
            'net_income' => $totalIncome - $totalExpense,
            'total_outstanding' => $totalOutstanding,
            'total_overdue' => $totalOverdue,
            'collection_rate' => $totalLoanAmount > 0 ? ($totalCollectionAmount / $totalLoanAmount) * 100 : 0
        ];
    }

    public function loadBranchFinancials()
    {
        $dateFrom = Carbon::parse($this->dateFrom)->startOfDay();
        $dateTo = Carbon::parse($this->dateTo)->endOfDay();
        
        $branches = Branch::with(['manager'])->get();
        
        $this->branchFinancials = $branches->map(function($branch) use ($dateFrom, $dateTo) {
            // Loan disbursements for this branch
            $branchLoans = Loan::whereHas('loanApplication.member', function($query) use ($branch) {
                $query->where('branch_id', $branch->id);
            })->whereBetween('disbursement_date', [$dateFrom, $dateTo])->sum('loan_amount');
            
            // Collections for this branch
            $branchCollections = Installment::whereHas('loan.loanApplication.member', function($query) use ($branch) {
                $query->where('branch_id', $branch->id);
            })->where('status', 'paid')
            ->whereBetween('paid_date', [$dateFrom, $dateTo])
            ->sum('installment_amount');
            
            // Branch transactions
            $branchIncome = BranchTransaction::where('branch_id', $branch->id)
                ->where('transaction_type', 'income')
                ->whereBetween('transaction_date', [$dateFrom, $dateTo])
                ->sum('amount');
                
            $branchExpense = BranchTransaction::where('branch_id', $branch->id)
                ->where('transaction_type', 'expense')
                ->whereBetween('transaction_date', [$dateFrom, $dateTo])
                ->sum('amount');
            
            // Outstanding for this branch
            $branchOutstanding = Installment::whereHas('loan.loanApplication.member', function($query) use ($branch) {
                $query->where('branch_id', $branch->id);
            })->where('status', 'pending')->sum('installment_amount');
            
            return [
                'id' => $branch->id,
                'name' => $branch->name,
                'manager' => $branch->manager?->name ?? 'Not Assigned',
                'loans' => $branchLoans,
                'collections' => $branchCollections,
                'income' => $branchIncome,
                'expense' => $branchExpense,
                'net_income' => $branchIncome - $branchExpense,
                'outstanding' => $branchOutstanding,
                'collection_rate' => $branchLoans > 0 ? ($branchCollections / $branchLoans) * 100 : 0
            ];
        })->sortByDesc('net_income')->values()->toArray();
    }

    public function loadMonthlyTrends()
    {
        $months = collect();
        for ($i = 11; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $startOfMonth = $month->copy()->startOfMonth();
            $endOfMonth = $month->copy()->endOfMonth();
            
            // Loan disbursements for the month
            $monthlyLoans = Loan::whereBetween('disbursement_date', [$startOfMonth, $endOfMonth]);
            if ($this->selectedBranch !== 'all') {
                $monthlyLoans->whereHas('loanApplication.member', function($query) {
                    $query->where('branch_id', $this->selectedBranch);
                });
            }
            $loanAmount = $monthlyLoans->sum('loan_amount');
            
            // Collections for the month
            $monthlyCollections = Installment::where('status', 'paid')
                ->whereBetween('paid_date', [$startOfMonth, $endOfMonth]);
            if ($this->selectedBranch !== 'all') {
                $monthlyCollections->whereHas('loan.loanApplication.member', function($query) {
                    $query->where('branch_id', $this->selectedBranch);
                });
            }
            $collectionAmount = $monthlyCollections->sum('installment_amount');
            
            // Branch income/expense for the month
            $monthlyIncomeQuery = BranchTransaction::where('transaction_type', 'income')
                ->whereBetween('transaction_date', [$startOfMonth, $endOfMonth]);
            $monthlyExpenseQuery = BranchTransaction::where('transaction_type', 'expense')
                ->whereBetween('transaction_date', [$startOfMonth, $endOfMonth]);
                
            if ($this->selectedBranch !== 'all') {
                $monthlyIncomeQuery->where('branch_id', $this->selectedBranch);
                $monthlyExpenseQuery->where('branch_id', $this->selectedBranch);
            }
            
            $monthlyIncome = $monthlyIncomeQuery->sum('amount');
            $monthlyExpense = $monthlyExpenseQuery->sum('amount');
            
            $months->push([
                'month' => $month->format('M Y'),
                'loans' => $loanAmount,
                'collections' => $collectionAmount,
                'income' => $monthlyIncome,
                'expense' => $monthlyExpense,
                'net_income' => $monthlyIncome - $monthlyExpense
            ]);
        }
        
        $this->monthlyTrends = $months->toArray();
    }

    public function openTransactionModal()
    {
        $this->resetTransactionForm();
        $this->showTransactionModal = true;
    }

    public function closeTransactionModal()
    {
        $this->showTransactionModal = false;
        $this->resetTransactionForm();
    }

    public function resetTransactionForm()
    {
        $this->transactionAmount = '';
        $this->transactionDescription = '';
        $this->transactionBranch = '';
        $this->transactionTypeModal = 'income';
        $this->resetErrorBag();
    }

    public function addTransaction()
    {
        $this->validate();

        try {
            DB::beginTransaction();

            BranchTransaction::create([
                'branch_id' => $this->transactionBranch,
                'transaction_type' => $this->transactionTypeModal,
                'amount' => $this->transactionAmount,
                'description' => $this->transactionDescription,
                'transaction_date' => now(),
                'created_by' => Auth::id()
            ]);

            DB::commit();
            
            session()->flash('message', 'Transaction added successfully!');
            $this->closeTransactionModal();
            $this->loadFinancialData();
            
        } catch (\Exception $e) {
            DB::rollBack();
            session()->flash('error', 'Error adding transaction: ' . $e->getMessage());
        }
    }

    public function exportFinancialReport()
    {
        // This method can be expanded to generate PDF/Excel reports
        session()->flash('message', 'Financial report export feature will be implemented.');
    }

    public function render()
    {
        $dateFrom = Carbon::parse($this->dateFrom)->startOfDay();
        $dateTo = Carbon::parse($this->dateTo)->endOfDay();
        
        // Get transactions for the table
        $transactionsQuery = BranchTransaction::with(['branch', 'creator'])
            ->whereBetween('transaction_date', [$dateFrom, $dateTo]);
            
        if ($this->selectedBranch !== 'all') {
            $transactionsQuery->where('branch_id', $this->selectedBranch);
        }
        
        if ($this->transactionType !== 'all') {
            $transactionsQuery->where('transaction_type', $this->transactionType);
        }
        
        if ($this->search) {
            $transactionsQuery->where('description', 'like', '%' . $this->search . '%');
        }
        
        $transactions = $transactionsQuery->orderBy('transaction_date', 'desc')->paginate(15);
        
        $branches = Branch::where('is_active', true)->get();
        
        return view('livewire.admin.financial-management', [
            'transactions' => $transactions,
            'branches' => $branches
        ]);
    }
}