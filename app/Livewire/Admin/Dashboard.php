<?php

namespace App\Livewire\Admin;

use Livewire\Component;
use App\Models\User;
use App\Models\Branch;
use App\Models\Member;
use App\Models\Loan;
use App\Models\Installment;
use App\Models\BranchTransaction;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class Dashboard extends Component
{
    public $systemStats = [];
    public $recentActivities = [];
    public $financialOverview = [];
    public $branchPerformance = [];
    public $monthlyTrends = [];

    public function mount()
    {
        $this->loadSystemStats();
        $this->loadRecentActivities();
        $this->loadFinancialOverview();
        $this->loadBranchPerformance();
        $this->loadMonthlyTrends();
    }

    public function loadSystemStats()
    {
        $this->systemStats = [
            'total_branches' => Branch::count(),
            'total_users' => User::count(),
            'total_field_officers' => User::where('role', 'field_officer')->count(),
            'total_managers' => User::where('role', 'manager')->count(),
            'total_members' => Member::where('is_active', true)->count(),
            'active_loans' => Loan::whereHas('installments', function($query) {
                $query->where('status', 'pending');
            })->count(),
            'total_loan_amount' => Loan::sum('loan_amount'),
            'pending_installments' => Installment::where('status', 'pending')
                ->where('due_date', '<=', now())
                 ->count(),
             'overdue_installments' => Installment::where('status', 'pending')
                 ->where('due_date', '<', now())
                ->count()
        ];
    }

    public function loadRecentActivities()
    {
        // Get recent member registrations
        $recentMembers = Member::with(['branch', 'creator'])
            ->latest()
            ->take(5)
            ->get()
            ->map(function($member) {
                return [
                    'type' => 'member_registration',
                    'title' => 'New Member Registered',
                    'description' => "Member {$member->name} registered at {$member->branch->name}",
                    'user' => $member->creator->name ?? 'System',
                    'time' => $member->created_at,
                    'icon' => 'user-plus',
                    'color' => 'green'
                ];
            });

        // Get recent loan applications
        $recentLoans = Loan::with(['loanApplication.member', 'loanApplication.member.branch'])
            ->latest()
            ->take(5)
            ->get()
            ->map(function($loan) {
                return [
                    'type' => 'loan_disbursement',
                    'title' => 'Loan Disbursed',
                    'description' => "৳{$loan->loan_amount} disbursed to " . ($loan->loanApplication?->member?->name ?? 'Unknown Member'),
                    'user' => 'System',
                    'time' => $loan->created_at,
                    'icon' => 'currency-dollar',
                    'color' => 'blue'
                ];
            });

        // Get recent installment collections
        $recentCollections = Installment::with(['loan.loanApplication.member', 'collector'])
            ->where('status', 'paid')
            ->latest('paid_date')
            ->take(5)
            ->get()
            ->map(function($installment) {
                return [
                    'type' => 'installment_collection',
                    'title' => 'Installment Collected',
                    'description' => "৳{$installment->installment_amount} collected from " . ($installment->loan?->loanApplication?->member?->name ?? 'Unknown Member'),
                    'user' => $installment->collector->name ?? 'System',
                    'time' => $installment->paid_date,
                    'icon' => 'cash',
                    'color' => 'emerald'
                ];
            });

        // Merge and sort all activities
        $allActivities = collect()
            ->merge($recentMembers)
            ->merge($recentLoans)
            ->merge($recentCollections)
            ->sortByDesc('time')
            ->take(10)
            ->values();

        $this->recentActivities = $allActivities->toArray();
    }

    public function loadFinancialOverview()
    {
        $currentMonth = now()->startOfMonth();
        $lastMonth = now()->subMonth()->startOfMonth();
        
        // Current month financial data
        $currentMonthLoans = Loan::where('disbursement_date', '>=', $currentMonth)->sum('loan_amount');
         $currentMonthCollections = Installment::where('status', 'paid')
             ->where('paid_date', '>=', $currentMonth)
             ->sum('installment_amount');
         
         // Last month financial data
         $lastMonthLoans = Loan::whereBetween('disbursement_date', [$lastMonth, $currentMonth])->sum('loan_amount');
         $lastMonthCollections = Installment::where('status', 'paid')
             ->whereBetween('paid_date', [$lastMonth, $currentMonth])
             ->sum('installment_amount');
        
        // Calculate growth percentages
        $loanGrowth = $lastMonthLoans > 0 ? (($currentMonthLoans - $lastMonthLoans) / $lastMonthLoans) * 100 : 0;
        $collectionGrowth = $lastMonthCollections > 0 ? (($currentMonthCollections - $lastMonthCollections) / $lastMonthCollections) * 100 : 0;
        
        $this->financialOverview = [
            'current_month_loans' => $currentMonthLoans,
            'current_month_collections' => $currentMonthCollections,
            'loan_growth' => round($loanGrowth, 2),
            'collection_growth' => round($collectionGrowth, 2),
            'total_outstanding' => Installment::where('status', 'pending')->sum('installment_amount'),
            'overdue_amount' => Installment::where('status', 'pending')
                 ->where('due_date', '<', now())
                 ->sum('installment_amount')
        ];
    }

    public function loadBranchPerformance()
    {
        $this->branchPerformance = Branch::with(['members', 'transactions'])
            ->get()
            ->map(function($branch) {
                $totalMembers = $branch->members()->where('is_active', true)->count();
                $activeLoans = Loan::whereHas('loanApplication.member', function($query) use ($branch) {
                    $query->where('branch_id', $branch->id);
                })->whereHas('installments', function($query) {
                    $query->where('status', 'pending');
                })->count();
                
                $monthlyCollection = Installment::whereHas('loan.loanApplication.member', function($query) use ($branch) {
                    $query->where('branch_id', $branch->id);
                })->where('status', 'paid')
                ->where('paid_date', '>=', now()->startOfMonth())
                ->sum('installment_amount');
                
                return [
                    'id' => $branch->id,
                    'name' => $branch->name,
                    'total_members' => $totalMembers,
                    'active_loans' => $activeLoans,
                    'monthly_collection' => $monthlyCollection,
                    'manager' => $branch->manager?->name ?? 'Not Assigned'
                ];
            })
            ->sortByDesc('monthly_collection')
            ->values()
            ->toArray();
    }

    public function loadMonthlyTrends()
    {
        $months = collect();
        for ($i = 11; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $startOfMonth = $month->copy()->startOfMonth();
            $endOfMonth = $month->copy()->endOfMonth();
            
            $loanAmount = Loan::whereBetween('disbursement_date', [$startOfMonth, $endOfMonth])->sum('loan_amount');
             $collectionAmount = Installment::where('status', 'paid')
                 ->whereBetween('paid_date', [$startOfMonth, $endOfMonth])
                 ->sum('installment_amount');
            
            $months->push([
                'month' => $month->format('M Y'),
                'loans' => $loanAmount,
                'collections' => $collectionAmount
            ]);
        }
        
        $this->monthlyTrends = $months->toArray();
    }

    public function render()
    {
        return view('livewire.admin.dashboard');
    }
}