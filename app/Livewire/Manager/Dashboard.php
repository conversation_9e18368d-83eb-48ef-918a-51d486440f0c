<?php

namespace App\Livewire\Manager;

use Livewire\Component;
use App\Models\Member;
use App\Models\Loan;
use App\Models\LoanApplication;
use App\Models\Installment;
use App\Models\BranchTransaction;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class Dashboard extends Component
{
    public $branchStats = [];
    public $chartData = [];
    public $recentActivities = [];
    
    public function mount()
    {
        $this->loadBranchStats();
        $this->loadChartData();
        $this->loadRecentActivities();
    }

    public function loadBranchStats()
    {
        $user = Auth::user();
        $branchId = $user->branch_id;

        // Field Officers count
        $fieldOfficersCount = User::where('branch_id', $branchId)
            ->whereHas('roles', function($query) {
                $query->where('name', 'field_officer');
            })->count();

        // Total active loans
        $activeLoansCount = Loan::whereHas('loanApplication.member', function($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })->where('status', 'active')->count();

        // Installment collection status
        $pendingInstallments = Installment::whereHas('loan.loanApplication.member', function($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })->where('status', 'pending')->count();

        $overdueInstallments = Installment::whereHas('loan.loanApplication.member', function($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })->where('status', 'overdue')->count();

        $collectedToday = Installment::whereHas('loan.loanApplication.member', function($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })->whereDate('paid_date', today())->sum('installment_amount');

        // Monthly targets vs achievements
        $monthlyTarget = 500000; // This should come from settings
        $monthlyCollected = BranchTransaction::where('branch_id', $branchId)
            ->where('entry_type', 'income')
            ->whereMonth('transaction_date', now()->month)
            ->whereYear('transaction_date', now()->year)
            ->sum('amount');

        $targetAchievement = $monthlyTarget > 0 ? ($monthlyCollected / $monthlyTarget) * 100 : 0;

        // Total members
        $totalMembers = Member::where('branch_id', $branchId)
            ->where('is_active', true)
            ->count();

        $this->branchStats = [
            'field_officers' => $fieldOfficersCount,
            'active_loans' => $activeLoansCount,
            'pending_installments' => $pendingInstallments,
            'overdue_installments' => $overdueInstallments,
            'collected_today' => $collectedToday,
            'monthly_target' => $monthlyTarget,
            'monthly_collected' => $monthlyCollected,
            'target_achievement' => round($targetAchievement, 1),
            'total_members' => $totalMembers,
        ];
    }

    public function loadChartData()
    {
        $user = Auth::user();
        $branchId = $user->branch_id;

        // Collection trends for last 7 days
        $collectionTrends = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $amount = BranchTransaction::where('branch_id', $branchId)
                ->where('transaction_type', 'installment_collection')
                ->whereDate('transaction_date', $date)
                ->sum('amount');
            
            $collectionTrends[] = [
                'date' => $date->format('M d'),
                'amount' => $amount
            ];
        }

        // Loan approval rates for last 6 months
        $approvalRates = [];
        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $totalApplications = LoanApplication::whereHas('member', function($query) use ($branchId) {
                $query->where('branch_id', $branchId);
            })->whereMonth('created_at', $date->month)
               ->whereYear('created_at', $date->year)
               ->count();

            $approvedApplications = LoanApplication::whereHas('member', function($query) use ($branchId) {
                $query->where('branch_id', $branchId);
            })->whereMonth('created_at', $date->month)
               ->whereYear('created_at', $date->year)
               ->where('status', 'approved')
               ->count();

            $rate = $totalApplications > 0 ? ($approvedApplications / $totalApplications) * 100 : 0;
            
            $approvalRates[] = [
                'month' => $date->format('M Y'),
                'rate' => round($rate, 1)
            ];
        }

        // Branch financial performance (Income vs Expense)
        $financialData = [];
        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            
            $income = BranchTransaction::where('branch_id', $branchId)
                ->where('entry_type', 'income')
                ->whereMonth('transaction_date', $date->month)
                ->whereYear('transaction_date', $date->year)
                ->sum('amount');

            $expense = BranchTransaction::where('branch_id', $branchId)
                ->where('entry_type', 'expense')
                ->whereMonth('transaction_date', $date->month)
                ->whereYear('transaction_date', $date->year)
                ->sum('amount');
            
            $financialData[] = [
                'month' => $date->format('M Y'),
                'income' => $income,
                'expense' => $expense
            ];
        }

        $this->chartData = [
            'collection_trends' => $collectionTrends,
            'approval_rates' => $approvalRates,
            'financial_performance' => $financialData
        ];
    }

    public function loadRecentActivities()
    {
        $user = Auth::user();
        $branchId = $user->branch_id;

        // Get recent loan applications
        $recentApplications = LoanApplication::whereHas('member', function($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })->with(['member'])
          ->orderBy('created_at', 'desc')
          ->limit(5)
          ->get();

        // Get recent installment collections
        $recentCollections = Installment::whereHas('loan.loanApplication.member', function($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })->with(['loan.loanApplication.member', 'collector'])
          ->where('status', 'paid')
          ->orderBy('paid_date', 'desc')
          ->limit(5)
          ->get();

        $activities = [];

        foreach ($recentApplications as $application) {
            $activities[] = [
                'type' => 'loan_application',
                'description' => "New loan application from {$application->member->name} for ৳" . number_format($application->applied_amount, 2),
                'time' => $application->created_at->diffForHumans(),
                'created_at' => $application->created_at,
                'status' => $application->status
            ];
        }

        foreach ($recentCollections as $collection) {
            $activities[] = [
                'type' => 'installment_collection',
                'description' => "Installment collected from " . ($collection->loan?->loanApplication?->member?->name ?? 'Unknown') . " - ৳" . number_format($collection->installment_amount, 2),
                'time' => $collection->paid_date->diffForHumans(),
                'created_at' => $collection->paid_date,
                'collector' => $collection->collector->name ?? 'Unknown'
            ];
        }

        // Sort by created_at desc
        usort($activities, function($a, $b) {
            return $b['created_at'] <=> $a['created_at'];
        });

        $this->recentActivities = array_slice($activities, 0, 10);
    }

    public function refreshData()
    {
        $this->loadBranchStats();
        $this->loadChartData();
        $this->loadRecentActivities();
        
        $this->dispatch('dataRefreshed');
    }

    public function render()
    {
        return view('livewire.manager.dashboard');
    }
}