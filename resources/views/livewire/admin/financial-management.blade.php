--- a/var/w/sonali/app/Livewire/Admin/FinancialManagement.php
+++ b/var/w/sonali/app/Livewire/Admin/FinancialManagement.php
@@ -121,14 +121,14 @@
         $totalCollectionAmount = $totalCollections->sum('installment_amount');
         
         // Branch transactions (income/expense)
-        $branchTransactions = BranchTransaction::whereBetween('transaction_date', [$dateFrom, $dateTo]);
+        $branchTransactionsQuery = BranchTransaction::whereBetween('transaction_date', [$dateFrom, $dateTo]);
         if ($this->selectedBranch !== 'all') {
-            $branchTransactions->where('branch_id', $this->selectedBranch);
+            $branchTransactionsQuery->where('branch_id', $this->selectedBranch);
         }
         
-        $totalIncome = $branchTransactions->where('transaction_type', 'income')->sum('amount');
-        $totalExpense = $branchTransactions->where('transaction_type', 'expense')->sum('amount');
+        $totalIncome = (clone $branchTransactionsQuery)->where('transaction_type', 'income')->sum('amount');
+        $totalExpense = (clone $branchTransactionsQuery)->where('transaction_type', 'expense')->sum('amount');
         
         // Outstanding amounts
         $outstandingQuery = Installment::where('status', 'pending');

